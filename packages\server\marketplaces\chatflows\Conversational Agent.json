{"description": "A conversational agent designed to use tools and chat model to provide responses", "usecases": ["Agent"], "framework": ["Langchain"], "nodes": [{"width": 300, "height": 143, "id": "calculator_1", "position": {"x": 800.5125025564965, "y": 72.40592063242738}, "type": "customNode", "data": {"id": "calculator_1", "label": "Calculator", "version": 1, "name": "calculator", "type": "Calculator", "baseClasses": ["Calculator", "Tool", "StructuredTool", "BaseLangChain"], "category": "Tools", "description": "Perform calculations on response", "inputParams": [], "inputAnchors": [], "inputs": {}, "outputAnchors": [{"id": "calculator_1-output-calculator-Calculator|Tool|StructuredTool|BaseLangChain", "name": "calculator", "label": "Calculator", "type": "Calculator | Tool | StructuredTool | BaseLangChain"}], "outputs": {}, "selected": false}, "positionAbsolute": {"x": 800.5125025564965, "y": 72.40592063242738}, "selected": false, "dragging": false}, {"width": 300, "height": 253, "id": "bufferMemory_1", "position": {"x": 607.6260576768354, "y": 584.7920541862369}, "type": "customNode", "data": {"id": "bufferMemory_1", "label": "Buffer Memory", "version": 2, "name": "bufferMemory", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseChatMemory", "BaseMemory"], "category": "Memory", "description": "Retrieve chat messages stored in database", "inputParams": [{"label": "Session Id", "name": "sessionId", "type": "string", "description": "If not specified, a random id will be used. Learn <a target=\"_blank\" href=\"https://docs.flowiseai.com/memory#ui-and-embedded-chat\">more</a>", "default": "", "additionalParams": true, "optional": true, "id": "bufferMemory_1-input-sessionId-string"}, {"label": "Memory Key", "name": "<PERSON><PERSON><PERSON>", "type": "string", "default": "chat_history", "additionalParams": true, "id": "bufferMemory_1-input-memoryKey-string"}], "inputAnchors": [], "inputs": {"sessionId": "", "memoryKey": "chat_history"}, "outputAnchors": [{"id": "bufferMemory_1-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "name": "bufferMemory", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "BufferMemory | BaseChatMemory | BaseMemory"}], "outputs": {}, "selected": false}, "positionAbsolute": {"x": 607.6260576768354, "y": 584.7920541862369}, "selected": false, "dragging": false}, {"width": 300, "height": 276, "id": "serpAPI_0", "position": {"x": 451.83740798447855, "y": 53.2843022150486}, "type": "customNode", "data": {"id": "serpAPI_0", "label": "Serp API", "version": 1, "name": "serpAPI", "type": "SerpAPI", "baseClasses": ["SerpAPI", "Tool", "StructuredTool"], "category": "Tools", "description": "Wrapper around SerpAPI - a real-time API to access Google search results", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["serp<PERSON><PERSON>"], "id": "serpAPI_0-input-credential-credential"}], "inputAnchors": [], "inputs": {}, "outputAnchors": [{"id": "serpAPI_0-output-serpAPI-SerpAPI|Tool|StructuredTool", "name": "serpAPI", "label": "SerpAPI", "type": "SerpAPI | Tool | StructuredTool"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 451.83740798447855, "y": 53.2843022150486}, "dragging": false}, {"width": 300, "height": 670, "id": "chatOpenAI_0", "position": {"x": 97.01321406237057, "y": 63.67664262280914}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"modelName": "gpt-3.5-turbo-16k", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 97.01321406237057, "y": 63.67664262280914}, "dragging": false}, {"width": 300, "height": 435, "id": "conversationalAgent_0", "position": {"x": 1191.1524476753796, "y": 324.2479396683294}, "type": "customNode", "data": {"id": "conversationalAgent_0", "label": "Conversational Agent", "version": 3, "name": "conversationalAgent", "type": "AgentExecutor", "baseClasses": ["AgentExecutor", "BaseChain", "Runnable"], "category": "Agents", "description": "Conversational agent for a chat model. It will utilize chat specific prompts", "inputParams": [{"label": "System Message", "name": "systemMessage", "type": "string", "rows": 4, "default": "Assistant is a large language model trained by OpenAI.\n\nAssistant is designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, Assistant is able to generate human-like text based on the input it receives, allowing it to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n\nAssistant is constantly learning and improving, and its capabilities are constantly evolving. It is able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. Additionally, Assistant is able to generate its own text based on the input it receives, allowing it to engage in discussions and provide explanations and descriptions on a wide range of topics.\n\nOverall, Assistant is a powerful system that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether you need help with a specific question or just want to have a conversation about a particular topic, Assistant is here to assist.", "optional": true, "additionalParams": true, "id": "conversationalAgent_0-input-systemMessage-string"}, {"label": "Max Iterations", "name": "maxIterations", "type": "number", "optional": true, "additionalParams": true, "id": "conversationalAgent_0-input-maxIterations-number"}], "inputAnchors": [{"label": "Allowed Tools", "name": "tools", "type": "Tool", "list": true, "id": "conversationalAgent_0-input-tools-Tool"}, {"label": "Chat Model", "name": "model", "type": "BaseChatModel", "id": "conversationalAgent_0-input-model-BaseChatModel"}, {"label": "Memory", "name": "memory", "type": "BaseChatMemory", "id": "conversationalAgent_0-input-memory-BaseChatMemory"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "conversationalAgent_0-input-inputModeration-Moderation"}], "inputs": {"inputModeration": "", "tools": ["{{calculator_1.data.instance}}", "{{serpAPI_0.data.instance}}"], "model": "{{chatOpenAI_0.data.instance}}", "memory": "{{bufferMemory_1.data.instance}}", "systemMessage": "Assistant is a large language model trained by OpenAI.\n\nAssistant is designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, Assistant is able to generate human-like text based on the input it receives, allowing it to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n\nAssistant is constantly learning and improving, and its capabilities are constantly evolving. It is able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. Additionally, Assistant is able to generate its own text based on the input it receives, allowing it to engage in discussions and provide explanations and descriptions on a wide range of topics.\n\nOverall, Assistant is a powerful system that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether you need help with a specific question or just want to have a conversation about a particular topic, Assistant is here to assist."}, "outputAnchors": [{"id": "conversationalAgent_0-output-conversationalAgent-AgentExecutor|BaseChain|Runnable", "name": "conversationalAgent", "label": "AgentExecutor", "type": "AgentExecutor | BaseChain | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1191.1524476753796, "y": 324.2479396683294}, "dragging": false}, {"id": "stickyNote_0", "position": {"x": 1190.081066428271, "y": 21.014152635796393}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "This agent works very similar to Tool Agent with slightly higher error rate.\n\nDifference being this agent uses prompt to instruct LLM using tools, as opposed to using LLM's function calling capability.\n\nFor LLMs that support function calling, it is recommended to use Tool Agent.\n\nExample question:\n1. What is the net worth of Elon Musk?\n2. Multiply the net worth by 2"}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 284, "selected": false, "positionAbsolute": {"x": 1190.081066428271, "y": 21.014152635796393}, "dragging": false}], "edges": [{"source": "calculator_1", "sourceHandle": "calculator_1-output-calculator-Calculator|Tool|StructuredTool|BaseLangChain", "target": "conversationalAgent_0", "targetHandle": "conversationalAgent_0-input-tools-Tool", "type": "buttonedge", "id": "calculator_1-calculator_1-output-calculator-Calculator|Tool|StructuredTool|BaseLangChain-conversationalAgent_0-conversationalAgent_0-input-tools-Tool", "data": {"label": ""}}, {"source": "serpAPI_0", "sourceHandle": "serpAPI_0-output-serpAPI-SerpAPI|Tool|StructuredTool", "target": "conversationalAgent_0", "targetHandle": "conversationalAgent_0-input-tools-Tool", "type": "buttonedge", "id": "serpAPI_0-serpAPI_0-output-serpAPI-SerpAPI|Tool|StructuredTool-conversationalAgent_0-conversationalAgent_0-input-tools-Tool", "data": {"label": ""}}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "target": "conversationalAgent_0", "targetHandle": "conversationalAgent_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel-conversationalAgent_0-conversationalAgent_0-input-model-BaseChatModel", "data": {"label": ""}}, {"source": "bufferMemory_1", "sourceHandle": "bufferMemory_1-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "target": "conversationalAgent_0", "targetHandle": "conversationalAgent_0-input-memory-BaseChatMemory", "type": "buttonedge", "id": "bufferMemory_1-bufferMemory_1-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory-conversationalAgent_0-conversationalAgent_0-input-memory-BaseChatMemory", "data": {"label": ""}}]}