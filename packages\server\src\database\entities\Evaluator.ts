import { Column, CreateDateColumn, <PERSON>tity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'
import { IEvaluator } from '../../Interface'

//1714808591644

@Entity()
export class Evaluator implements IEvaluator {
    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column()
    name: string

    @Column()
    type: string

    @Column()
    config: string

    @CreateDateColumn()
    createdDate: Date

    @UpdateDateColumn()
    updatedDate: Date

    @Column({ nullable: true, type: 'text' })
    workspaceId?: string
}
